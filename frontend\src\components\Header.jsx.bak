import { useState, useContext, useEffect, useRef } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { AuthContext } from '../context/AuthContext';

const Header = () => {
  const { isAuthenticated, logout } = useContext(AuthContext);
  const navigate = useNavigate();
  const location = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [activeItem, setActiveItem] = useState('/');
  const [authMenuOpen, setAuthMenuOpen] = useState(false);
  const authMenuRef = useRef(null);

  // Detect scroll position for transparent to solid navbar transition
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 20) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };
    window.addEventListener('scroll', handleScroll);
    
    // Set active menu item based on current path
    setActiveItem(location.pathname);

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [location.pathname]);

  // Close auth menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (authMenuRef.current && !authMenuRef.current.contains(event.target)) {
        setAuthMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const toggleAuthMenu = () => {
    setAuthMenuOpen(!authMenuOpen);
  };

  // Determine if a nav item is active
  const isActive = (path) => {
    return activeItem === path;
  };

  return (
    <header className={`fixed top-0 w-full z-50 transition-all duration-300 ${scrolled ? 'bg-dark-bg-secondary/95 backdrop-blur-md shadow-dark-lg' : 'bg-transparent'}`}>
      <div className="container mx-auto px-4 max-w-full lg:max-w-screen-2xl">
        <div className="flex justify-between items-center py-4">
          {/* Logo */}
          <div className="flex items-center">
            <Link to="/" className="flex items-center group relative">
              <div className="absolute -inset-2 bg-gradient-to-r from-dark-accent-primary/20 to-dark-accent-secondary/20 rounded-full blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative">
                <img src="/img/logo.png" alt="FileForge Logo" className="h-10 w-auto mr-2 transform group-hover:scale-105 transition-transform duration-300" />
                {/* Animated highlight */}
                <div className="absolute -inset-0.5 bg-dark-accent-primary/20 rounded-full opacity-0 group-hover:opacity-100 animate-pulse-shadow transition-opacity duration-300"></div>
              </div>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-2">
            <div className="relative bg-dark-bg-primary/40 backdrop-blur-sm rounded-full p-1 border border-dark-border/20">
              <div className="flex space-x-1 relative z-10">
                <NavItem 
                  to="/" 
                  label="Home" 
                  isActive={isActive('/')} 
                  onClick={() => setActiveItem('/')}
                />
                
                {isAuthenticated && (
                  <NavItem 
                    to="/dashboard" 
                    label="Dashboard" 
                    isActive={isActive('/dashboard')} 
                    onClick={() => setActiveItem('/dashboard')}
                  />
                )}
              </div>
            </div>

            {/* Auth Buttons */}
            <div className="ml-4 flex items-center">
              {isAuthenticated ? (
                <button 
                  onClick={handleLogout}
                  className="group relative px-5 py-2.5 overflow-hidden rounded-lg bg-dark-bg-primary border border-dark-border shadow-sm transition-all duration-300 hover:border-dark-accent-primary/50 hover:shadow-dark-accent-primary/10"
                >
                  <div className="absolute inset-0 w-3 bg-gradient-to-r from-dark-accent-primary to-dark-accent-secondary opacity-0 group-hover:opacity-20 transition-opacity duration-300 group-hover:w-full"></div>
                  <span className="relative flex items-center justify-center text-dark-text-primary text-sm">
                    <svg className="w-4 h-4 mr-2 transform group-hover:rotate-12 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                    </svg>
                    Log Out
                  </span>
                </button>
              ) : (
                <div className="relative" ref={authMenuRef}>
                  <button 
                    onClick={toggleAuthMenu}
                    className="relative overflow-hidden px-5 py-2.5 rounded-lg bg-gradient-to-r from-dark-accent-primary to-dark-accent-secondary text-white font-medium text-sm shadow-md transition-all duration-300 hover:shadow-lg hover:shadow-dark-accent-primary/30 transform hover:-translate-y-0.5 flex items-center"
                  >
                    <span className="absolute inset-0 bg-white opacity-0 hover:opacity-20 transition-opacity duration-300"></span>
                    <span className="relative flex items-center">
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                      Account
                      <svg className={`w-4 h-4 ml-1 transition-transform duration-200 ${authMenuOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                      </svg>
                    </span>
                  </button>
                  
                  {/* Auth dropdown menu */}
                  <div className={`absolute right-0 mt-2 w-48 rounded-lg shadow-dark-xl bg-dark-bg-secondary border border-dark-border overflow-hidden transition-all duration-200 transform origin-top-right ${authMenuOpen ? 'scale-100 opacity-100' : 'scale-95 opacity-0 pointer-events-none'}`}>
                    <Link 
                      to="/login" 
                      className="block px-4 py-3 text-dark-text-primary hover:bg-dark-bg-primary transition-colors duration-200 flex items-center"
                      onClick={() => setAuthMenuOpen(false)}
                    >
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                      </svg>
                      Log In
                    </Link>
                  </div>
                </div>
              )}
            </div>
          </nav>

          {/* Mobile Menu Button */}
          <button 
            className={`md:hidden relative z-20 w-10 h-10 flex items-center justify-center rounded-full ${isMobileMenuOpen ? 'bg-dark-accent-primary text-white' : 'text-dark-text-secondary bg-dark-bg-primary/40'} transition-all duration-300`}
            onClick={toggleMobileMenu}
            aria-label="Menu"
          >
            <div className="w-6 h-6 flex flex-col items-center justify-center overflow-hidden">
              <div className="flex flex-col justify-center overflow-hidden w-full h-full transform transition-all duration-300">
                <div className={`h-0.5 w-5 bg-current transform transition-all duration-300 ${isMobileMenuOpen ? 'translate-y-0.5 rotate-45' : '-translate-y-1'}`}></div>
                <div className={`h-0.5 w-5 bg-current transform transition-all duration-300 ${isMobileMenuOpen ? 'opacity-0' : 'opacity-100'}`}></div>
                <div className={`h-0.5 w-5 bg-current transform transition-all duration-300 ${isMobileMenuOpen ? 'translate-y-0 -rotate-45' : 'translate-y-1'}`}></div>
              </div>
            </div>
          </button>
        </div>

        {/* Mobile Navigation */}
        <div className={`fixed inset-0 z-10 md:hidden transition-all duration-300 ${isMobileMenuOpen ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none'}`}>
          <div className="absolute inset-0 bg-dark-bg-primary/80 backdrop-blur-md" onClick={() => setIsMobileMenuOpen(false)}></div>
          <div className={`absolute right-0 top-0 w-3/4 h-full bg-dark-bg-secondary border-l border-dark-border shadow-dark-xl transform transition-transform duration-300 ${isMobileMenuOpen ? 'translate-x-0' : 'translate-x-full'}`}>
            <div className="py-20 px-6">
              <nav className="flex flex-col space-y-4">
                <MobileNavItem
                  to="/"
                  label="Home"
                  isActive={isActive('/')}
                  onClick={() => {
                    setActiveItem('/');
                    setIsMobileMenuOpen(false);
                  }}
                  icon={
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                    </svg>
                  }
                />
                
                {isAuthenticated && (
                  <MobileNavItem
                    to="/dashboard"
                    label="Dashboard"
                    isActive={isActive('/dashboard')}
                    onClick={() => {
                      setActiveItem('/dashboard');
                      setIsMobileMenuOpen(false);
                    }}
                    icon={
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
                      </svg>
                    }
                  />
                )}
                
                <div className="h-px w-full bg-dark-border my-4"></div>
                
                {isAuthenticated ? (
                  <button 
                    onClick={() => {
                      handleLogout();
                      setIsMobileMenuOpen(false);
                    }}
                    className="flex items-center px-4 py-3 rounded-lg border border-dark-border/50 bg-dark-bg-primary hover:bg-dark-hover hover:border-dark-accent-primary/50 text-dark-text-primary transition-all duration-300"
                  >
                    <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                    </svg>
                    Log Out
                  </button>
                ) : (
                  <>
                    <div className="text-dark-text-secondary mb-2 px-4 text-sm font-medium">Account</div>
                    <div className="space-y-3">
                      <Link 
                        to="/login"
                        onClick={() => setIsMobileMenuOpen(false)}
                        className="flex items-center px-4 py-3 rounded-lg border border-dark-border/50 bg-dark-bg-primary hover:bg-dark-hover hover:border-dark-accent-primary/50 text-dark-text-primary transition-all duration-300"
                      >
                        <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                        </svg>
                        Log In
                      </Link>
                    </div>
                  </>
                )}
              </nav>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

// Desktop Navigation Item Component
const NavItem = ({ to, label, isActive, onClick, isButton }) => {
  const content = (
    <span className="relative z-10 px-4 py-2 flex items-center text-sm font-medium transition-colors duration-200">
      {label}
    </span>
  );

  const activeClasses = "text-white";
  const inactiveClasses = "text-dark-text-secondary hover:text-dark-text-primary";
  const classes = `rounded-full relative ${isActive ? activeClasses : inactiveClasses}`;

  // Active item pill background
  const activePill = isActive && (
    <span className="absolute inset-0 rounded-full bg-gradient-to-r from-dark-accent-primary to-dark-accent-secondary transition-all duration-300"></span>
  );

  if (isButton) {
    return (
      <button onClick={onClick} className={classes}>
        {activePill}
        {content}
      </button>
    );
  }

  return (
    <Link to={to} onClick={onClick} className={classes}>
      {activePill}
      {content}
    </Link>
  );
};

// Mobile Navigation Item Component
const MobileNavItem = ({ to, label, isActive, onClick, isButton, icon }) => {
  const classes = `flex items-center w-full px-4 py-3 rounded-lg transition-all duration-200 ${
    isActive 
      ? 'bg-gradient-to-r from-dark-accent-primary to-dark-accent-secondary text-white font-medium' 
      : 'text-dark-text-secondary hover:bg-dark-bg-primary hover:text-dark-text-primary'
  }`;

  const content = (
    <>
      <span className="mr-3">{icon}</span>
      <span>{label}</span>
    </>
  );

  if (isButton) {
    return (
      <button className={classes} onClick={onClick}>
        {content}
      </button>
    );
  }

  return (
    <Link to={to} className={classes} onClick={onClick}>
      {content}
    </Link>
  );
};

export default Header; 