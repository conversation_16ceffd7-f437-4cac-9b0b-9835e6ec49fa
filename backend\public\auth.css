/* Authentication Pages Styles */
body {
    background-color: #f8f9fa !important;
    font-family: system-ui, -apple-system, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", sans-serif;
    color: #333;
    min-height: 100vh;
    display: flex !important;
    flex-direction: column;
    padding-top: 0;
    margin: 0;
}

/* Navbar styling for auth pages */
.auth-page .navbar {
    padding: 0.5rem 1rem !important;
    background-color: #212529 !important;
    border-bottom: 1px solid rgba(0,0,0,0.1);
    height: auto !important;
    min-height: 60px !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.auth-page .navbar-brand {
    font-size: 1.25rem;
    font-weight: 600;
    color: white;
}

.auth-page .navbar-nav {
    margin-left: auto;
}

.auth-page .nav-link {
    padding: 0.5rem 1rem !important;
    font-size: 0.9rem;
}

.auth-page .container {
    flex: 1;
    display: flex !important;
    align-items: center;
    justify-content: center;
    padding-top: 60px; /* Account for smaller navbar */
    padding-bottom: 40px;
}

.card {
    width: 100%;
    max-width: 450px;
    border: none;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-top: -40px; /* Move card up a bit for better visual balance */
}

.card-header {
    background-color: #4285f4;
    color: white;
    padding: 20px;
    font-weight: bold;
}

.card-header h4 {
    margin: 0;
    font-size: 1.5rem;
}

.card-body {
    padding: 30px;
}

.card-footer {
    background-color: rgba(0, 0, 0, 0.03);
    padding: 15px;
}

.form-label {
    font-weight: 500;
    margin-bottom: 8px;
}

.form-control {
    padding: 12px;
    border-radius: 5px;
    border: 1px solid #dee2e6;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: #4285f4;
    box-shadow: 0 0 0 0.2rem rgba(66, 133, 244, 0.25);
}

.btn-primary {
    background-color: #4285f4;
    border-color: #4285f4;
    padding: 12px;
    font-weight: bold;
    transition: background-color 0.15s ease-in-out;
}

.btn-primary:hover {
    background-color: #3367d6;
    border-color: #3367d6;
}

.d-grid .btn {
    display: block;
    width: 100%;
}

.card-footer a {
    color: #4285f4;
    text-decoration: none;
    font-weight: 500;
}

.card-footer a:hover {
    text-decoration: underline;
}

/* Alert boxes */
.alert {
    padding: 12px 20px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 5px;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

/* Media queries for responsiveness */
@media (max-width: 576px) {
    .card {
        margin: -20px 15px 0;
    }
    
    .card-body {
        padding: 20px;
    }
} 