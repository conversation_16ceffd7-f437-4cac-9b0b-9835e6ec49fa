{"dependencies": {"bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "cloudinary": "^2.6.0", "connect-flash": "^0.1.1", "connect-mongo": "^5.1.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "ejs": "^3.1.10", "express": "^4.19.2", "express-fileupload": "^1.5.1", "express-session": "^1.18.1", "jsonwebtoken": "^9.0.2", "mailersend": "^2.5.0", "mongoose": "^8.13.3", "moongose": "^1.0.0", "multer": "^1.4.5-lts.1", "node-fetch": "^2.7.0", "nodemailer": "^6.9.13", "passport": "^0.7.0", "passport-local": "^1.0.0", "uuid": "^9.0.1"}, "name": "fileforge_api", "version": "1.0.0", "main": "server.js", "license": "MIT", "scripts": {"dev": "nodemon server.js --ignore uploads/ --ignore tmp/", "serve": "node server.js"}, "devDependencies": {"nodemon": "^3.1.0"}}