/* Dashboard specific styles */
body {
    background-color: #f8f9fa !important;
    font-family: system-ui, -apple-system, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", sans-serif;
    color: #333;
    display: block !important;
    min-height: 100vh;
    overflow-x: hidden;
    height: auto !important;
}

.dashboard {
    padding: 30px 0;
    display: block !important;
}

.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    overflow: hidden;
    display: block !important;
    width: 100% !important;
    margin-bottom: 30px;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    padding: 15px 20px;
}

.card-body {
    padding: 20px;
}

.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    border-bottom: 2px solid #e9ecef;
    font-weight: 600;
    color: #495057;
}

.table td {
    vertical-align: middle;
    border-color: #e9ecef;
}

.table-hover tbody tr:hover {
    background-color: rgba(0,0,0,0.02);
}

.file-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.file-info {
    max-width: 250px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.btn {
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
    font-weight: 500;
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.btn-primary {
    color: #fff;
    background-color: #4285f4;
    border-color: #4285f4;
}

.btn-primary:hover {
    background-color: #3367d6;
    border-color: #3367d6;
}

.btn-outline-primary, .btn-outline-success, .btn-outline-danger {
    background-color: transparent;
}

.btn-outline-primary {
    color: #4285f4;
    border-color: #4285f4;
}

.btn-outline-success {
    color: #34a853;
    border-color: #34a853;
}

.btn-outline-danger {
    color: #ea4335;
    border-color: #ea4335;
}

.btn-outline-primary:hover {
    color: #fff;
    background-color: #4285f4;
    border-color: #4285f4;
}

.btn-outline-success:hover {
    color: #fff;
    background-color: #34a853;
    border-color: #34a853;
}

.btn-outline-danger:hover {
    color: #fff;
    background-color: #ea4335;
    border-color: #ea4335;
}

/* Empty state styling */
.text-center {
    text-align: center !important;
}

.text-muted {
    color: #6c757d !important;
}

/* Copy URL input styling */
.input-group {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: stretch;
    width: 100%;
}

.input-group .form-control {
    position: relative;
    flex: 1 1 auto;
    width: 1%;
    min-width: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.input-group .btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .file-info {
        max-width: 150px;
    }
    
    .d-flex {
        flex-wrap: wrap;
    }
    
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
}

/* Modal fixes */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1050;
    display: none;
    width: 100%;
    height: 100%;
    overflow: hidden;
    outline: 0;
}

.modal-dialog {
    position: relative;
    width: auto;
    margin: 0.5rem;
    pointer-events: none;
}

.modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0,0,0,0.2);
    border-radius: 0.3rem;
    outline: 0;
}

.modal-header, .modal-body, .modal-footer {
    padding: 1rem;
}

.modal-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    border-bottom: 1px solid #dee2e6;
}

.modal-footer {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: flex-end;
    border-top: 1px solid #dee2e6;
}

@media (min-width: 576px) {
    .modal-dialog {
        max-width: 500px;
        margin: 1.75rem auto;
    }
} 