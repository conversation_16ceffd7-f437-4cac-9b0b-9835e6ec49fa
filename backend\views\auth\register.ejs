<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card shadow-lg">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">Register for FileForge</h4>
            </div>
            <div class="card-body p-4">
                <form action="/auth/register" method="POST">
                    <input type="hidden" name="returnTo" value="<%= typeof returnTo !== 'undefined' ? returnTo : '/dashboard' %>">
                    <div class="mb-3">
                        <label for="name" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="name" name="name" placeholder="Enter your full name" value="<%= typeof name != 'undefined' ? name : '' %>" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" class="form-control" id="email" name="email" placeholder="Enter your email" value="<%= typeof email != 'undefined' ? email : '' %>" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" placeholder="Create a password (min. 6 characters)" required>
                    </div>
                    <div class="mb-3">
                        <label for="confirmPassword" class="form-label">Confirm Password</label>
                        <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" placeholder="Confirm your password" required>
                    </div>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">Register</button>
                    </div>
                </form>
            </div>
            <div class="card-footer bg-light text-center">
                <p class="mb-0">Already have an account? <a href="/auth/login<%= typeof returnTo !== 'undefined' ? '?returnTo=' + encodeURIComponent(returnTo) : '' %>">Login here</a></p>
            </div>
        </div>
    </div>
</div> 