<div class="dashboard">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1>My Files</h1>
        </div>
        <div class="col-md-4 text-end">
            <a href="/share" class="btn btn-primary btn-lg shadow-sm">
                <i class="fas fa-upload me-2"></i>Share a New File
            </a>
        </div>
    </div>

    <!-- If user has no files, show a welcome message with a prominent share button -->
    <% if(!files || files.length === 0) { %>
        <div class="card shadow-sm border-0 mb-4 bg-light">
            <div class="card-body text-center py-5">
                <i class="fas fa-cloud-upload-alt fa-5x text-primary mb-4"></i>
                <h2>Welcome to your FileForge Dashboard!</h2>
                <p class="lead mb-4">Ready to share your first file? Click the button below to get started.</p>
                <a href="/share" class="btn btn-primary btn-lg px-5 shadow">
                    <i class="fas fa-upload me-2"></i>Share Your First File
                </a>
            </div>
        </div>
    <% } %>

    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-body">
                    <% if(files && files.length > 0) { %>
                        <div class="table-responsive">
                            <table class="table table-hover align-middle">
                                <thead class="table-light">
                                    <tr>
                                        <th>File Name</th>
                                        <th>Size</th>
                                        <th>Uploaded On</th>
                                        <th>Link</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <% files.forEach(file => { %>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="file-icon me-2">
                                                        <i class="fas fa-file-alt fa-2x text-primary"></i>
                                                    </div>
                                                    <div class="file-info">
                                                        <a href="/dashboard/file/<%= file._id %>" class="fw-bold text-decoration-none">
                                                            <%= file.filename %>
                                                        </a>
                                                    </div>
                                                </div>
                                            </td>
                                            <td><%= formatBytes(file.size) %></td>
                                            <td><%= new Date(file.createdAt).toLocaleString() %></td>
                                            <td>
                                                <div class="input-group">
                                                    <input type="text" class="form-control form-control-sm" value="<%= process.env.APP_BASE_URL || 'http://localhost:3000' %>/files/<%= file.uuid %>" readonly>
                                                    <button class="btn btn-outline-secondary btn-sm copy-btn" type="button" data-url="<%= process.env.APP_BASE_URL || 'http://localhost:3000' %>/files/<%= file.uuid %>">
                                                        <i class="fas fa-copy"></i>
                                                    </button>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex">
                                                    <a href="/files/<%= file.uuid %>" class="btn btn-sm btn-outline-primary me-2" target="_blank">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="/files/download/<%= file.uuid %>" class="btn btn-sm btn-outline-success me-2">
                                                        <i class="fas fa-download"></i>
                                                    </a>
                                                    <button class="btn btn-sm btn-outline-danger delete-file" data-file-id="<%= file._id %>">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <% }); %>
                                </tbody>
                            </table>
                        </div>
                    <% } else { %>
                        <div class="text-center py-5">
                            <i class="fas fa-folder-open fa-4x text-muted mb-3"></i>
                            <h3 class="text-muted">No files uploaded yet</h3>
                            <p>Start uploading files to see them listed here</p>
                            <a href="/share" class="btn btn-primary mt-3">
                                <i class="fas fa-upload me-2"></i>Upload Your First File
                            </a>
                        </div>
                    <% } %>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this file? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">Delete</button>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Copy URL functionality
        const copyButtons = document.querySelectorAll('.copy-btn');
        copyButtons.forEach(button => {
            button.addEventListener('click', function() {
                const url = this.getAttribute('data-url');
                navigator.clipboard.writeText(url)
                    .then(() => {
                        // Show tooltip or notification
                        this.innerHTML = '<i class="fas fa-check"></i>';
                        setTimeout(() => {
                            this.innerHTML = '<i class="fas fa-copy"></i>';
                        }, 2000);
                    })
                    .catch(err => {
                        console.error('Failed to copy text: ', err);
                    });
            });
        });

        // Delete file functionality
        const deleteButtons = document.querySelectorAll('.delete-file');
        const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        let fileToDelete = null;

        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                fileToDelete = this.getAttribute('data-file-id');
                deleteModal.show();
            });
        });

        document.getElementById('confirmDelete').addEventListener('click', function() {
            if (fileToDelete) {
                // Send delete request
                fetch(`/dashboard/file/${fileToDelete}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        deleteModal.hide();
                        // Reload page or remove element from DOM
                        window.location.reload();
                    } else {
                        alert('Failed to delete file');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while deleting the file');
                });
            }
        });
    });
</script> 