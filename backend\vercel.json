{"version": 2, "builds": [{"src": "server.js", "use": "@vercel/node"}], "routes": [{"src": "/api/(.*)", "dest": "server.js", "headers": {"Access-Control-Allow-Origin": "https://fileforge-indol.vercel.app", "Access-Control-Allow-Methods": "GET,POST,PUT,DELETE,OPTIONS,PATCH", "Access-Control-Allow-Headers": "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, X-File-Name, X-File-Size, X-File-Type", "Access-Control-Allow-Credentials": "true"}}, {"src": "/files/(.*)", "dest": "server.js", "headers": {"Access-Control-Allow-Origin": "https://fileforge-indol.vercel.app", "Access-Control-Allow-Methods": "GET,POST,PUT,DELETE,OPTIONS,PATCH", "Access-Control-Allow-Headers": "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, X-File-Name, X-File-Size, X-File-Type", "Access-Control-Allow-Credentials": "true"}}, {"src": "/uploads/(.*)", "dest": "server.js", "headers": {"Access-Control-Allow-Origin": "https://fileforge-indol.vercel.app", "Access-Control-Allow-Methods": "GET,POST,PUT,DELETE,OPTIONS,PATCH", "Access-Control-Allow-Headers": "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, X-File-Name, X-File-Size, X-File-Type", "Access-Control-Allow-Credentials": "true"}}, {"src": "/(.*)", "dest": "server.js", "headers": {"Access-Control-Allow-Origin": "https://fileforge-indol.vercel.app", "Access-Control-Allow-Methods": "GET,POST,PUT,DELETE,OPTIONS,PATCH", "Access-Control-Allow-Headers": "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, X-File-Name, X-File-Size, X-File-Type", "Access-Control-Allow-Credentials": "true"}}]}