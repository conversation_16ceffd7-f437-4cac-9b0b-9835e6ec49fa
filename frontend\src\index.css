@import "tailwindcss";

/* Custom theme variables */
:root {
  /* Dark theme colors */
  --dark-bg-primary: #0f172a;
  --dark-bg-secondary: #1e293b;
  --dark-accent-primary: #6366f1;
  --dark-accent-secondary: #4f46e5;
  --dark-text-primary: #f1f5f9;
  --dark-text-secondary: #94a3b8;
  --dark-border: #334155;
  --dark-hover: #2d3748;
  
  font-family: 'Inter', system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  
  color: var(--dark-text-primary);
  background-color: var(--dark-bg-primary);
  
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom animations */
@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
  100% { transform: translateY(0px); }
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4); }
  70% { box-shadow: 0 0 0 20px rgba(99, 102, 241, 0); }
  100% { box-shadow: 0 0 0 0 rgba(99, 102, 241, 0); }
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-shadow {
  animation: pulse 2s infinite;
}

.gradient-animate {
  background-size: 200% 200%;
  animation: gradient-shift 15s ease infinite;
}

.shimmer {
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0.03) 25%, 
    rgba(255, 255, 255, 0.1) 50%, 
    rgba(255, 255, 255, 0.03) 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

a {
  font-weight: 500;
  color: var(--dark-accent-primary);
  text-decoration: inherit;
  transition: color 0.2s ease;
}
a:hover {
  color: var(--dark-accent-secondary);
}

body {
  margin: 0;
  display: block;
  width: 100%;
  min-height: 100vh;
  background-color: var(--dark-bg-primary);
  color: var(--dark-text-primary);
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
  font-weight: 700;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

/* Add custom style for glass morphism effect */
.glassmorphism {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}
