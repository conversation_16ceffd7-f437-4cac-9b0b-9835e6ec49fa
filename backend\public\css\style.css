:root {
  --main-bg-color: #000000;
  /* Black background */
  --text-color: #f5f5dc;
  /* Beige text color */
  --light-shadow: rgba(0, 0, 0, 0.1);
  /* Light shadow color */
  --light-border: #d3d3d3;
  /* Light border color */
  --light-blue: #03a9f4;
  --dark-blue: #028bca;
  --beige-cool: #76ABAE;
}

body {
  font-family: system-ui;
  background: var(--main-bg-color);
  height: 98vh;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-color);
  /* Set text color */
}

.logo {
  position: absolute;
  top: 10px;
  left: 10px;
  width: 100px;
}

section.download {
  background: grey;
  width: 430px;
  max-width: 90%;
  border-radius: 25px;
  box-shadow: 0px 10px 10px 0px var(--light-shadow);
  /* Lighter shadow */
  padding: 2rem;
  text-align: center;
}

.download__icon {
  height: 8rem;
}

.download__meta h4 {
  margin-bottom: 0;
  line-height: 1.3;
}

.send-btn-container a {
  display: inline-block;
  font-size: 18px;
  padding: 8px 40px;
  margin-top: 15px;
  background: var(--beige-cool);
  text-decoration: none;
  border: none;
  border-radius: 5px;
  color: #fff;
  cursor: pointer;
  transition: all .3s ease-in-out;
}

.send-btn-container a:hover {
  background: var(--main-bg-color);
}

/* Update border and shadow properties */
section.download {
  box-shadow: 0px 10px 10px 0px var(--light-shadow);
  border: 2px solid var(--light-border);
}