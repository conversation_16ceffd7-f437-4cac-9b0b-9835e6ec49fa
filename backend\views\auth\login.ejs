<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card shadow-lg">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">Login to FileForge</h4>
            </div>
            <div class="card-body p-4">
                <form action="/auth/login" method="POST">
                    <input type="hidden" name="returnTo" value="<%= typeof returnTo !== 'undefined' ? returnTo : '/dashboard' %>">
                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" class="form-control" id="email" name="email" placeholder="Enter your email" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" placeholder="Enter your password" required>
                    </div>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">Login</button>
                    </div>
                </form>
            </div>
            <div class="card-footer bg-light text-center">
                <p class="mb-0">Don't have an account? <a href="/auth/register<%= typeof returnTo !== 'undefined' ? '?returnTo=' + encodeURIComponent(returnTo) : '' %>">Register here</a></p>
            </div>
        </div>
    </div>
</div> 