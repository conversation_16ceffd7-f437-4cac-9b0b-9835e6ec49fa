<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <link rel="shortcut icon" href="/img/favicon.ico" type="image/x-icon">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FileForge - Share Your Files</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="style.css">
    <style>
        /* Fix any display issues */
        body {
            display: flex !important;
            flex-direction: column !important;
            min-height: 100vh !important;
            padding-top: 60px !important;
            overflow-y: auto !important;
            background-color: #000000 !important;
        }
        .navbar {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
            padding: 10px 20px !important;
            background-color: rgba(0, 0, 0, 0.8) !important;
            z-index: 1000 !important;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3) !important;
        }
        
        .navbar .nav-links {
            display: flex !important;
            gap: 20px !important;
        }
        
        .navbar .nav-links a {
            color: white !important;
            text-decoration: none !important;
            font-weight: bold !important;
            transition: color 0.3s ease !important;
            padding: 8px 12px !important;
            border-radius: 5px !important;
        }
        
        .navbar .nav-links a:hover {
            color: #4285f4 !important;
            background-color: rgba(255, 255, 255, 0.1) !important;
        }
        
        .upload-container {
            margin-top: 40px !important;
            display: flex !important;
            flex-direction: column !important;
            align-items: center !important;
            justify-content: center !important;
            background: ghostwhite !important;
            border-radius: 25px !important;
            box-shadow: 0px 20px 20px 0px rgba(222, 184, 135, 0.5) !important;
            padding: 30px !important;
            width: 600px !important;
            max-width: 90% !important;
        }
        
        .logo {
            position: relative !important;
            top: auto !important;
            left: auto !important;
            margin-bottom: 30px !important;
            width: 100px !important;
        }
        
        .drop-zone {
            width: 100% !important;
            min-height: 200px !important;
            display: flex !important;
            flex-direction: column !important;
            align-items: center !important;
            justify-content: center !important;
            border: 2px dashed #6c757d !important;
            border-radius: 10px !important;
            margin: 20px 0 !important;
            padding: 30px !important;
            transition: 0.2s all ease-in !important;
        }
        
        .image-vector {
            width: 100% !important;
            height: 40vh !important;
            background: url(/img/upload.svg) no-repeat center !important;
            background-size: contain !important;
            margin-top: 20px !important;
        }
    </style>
</head>

<body>
    <!-- Add navbar -->
    <div class="navbar">
        <div class="logo-container">
            <img src="./img/logo.png" alt="FileForge logo" style="height: 30px;">
        </div>
        <div class="nav-links">
            <a href="/"><i class="fas fa-home"></i> Home</a>
            <a href="/dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
            <a href="/auth/logout"><i class="fas fa-sign-out-alt"></i> Logout</a>
        </div>
    </div>
    
    <div class="container d-flex flex-column align-items-center justify-content-center">
        <section class="upload-container">
            <h2 class="text-center mb-4 fw-bold">Share Your Files</h2>
            <p class="text-center text-muted mb-4">Drag and drop your file or click browse to select a file to share.</p>
            
            <form action="">
                <div class="drop-zone">
                    <div class="icon-container">
                        <img src="img/file.png" draggable="false" class="center" alt="File Icon">
                        <img src="img/file.png" draggable="false" class="left" alt="File Icon">
                        <img src="img/file.png" draggable="false" class="right" alt="File Icon">
                    </div>
                    <input type="file" id="fileInput">
                    <div class="title mt-3">Drop your Files here or, <span id="browseBtn" class="fw-bold">browse</span></div>
                </div>
            </form>
            <div class="progress-container">
                <div class="bg-progress"></div>

                <div class="inner-container">
                    <div class="status">Uploading...</div>
                    <div class="percent-container">
                        <span class="percentage" id="progressPercent">0</span>%
                    </div>
                    <div class="progress-bar"></div>
                </div>
            </div>
            <div class="sharing-container">
                <p class="expire">Link expires in 24 hrs</p>
                <div class="input-container">
                    <input type="text" id="fileURL" readonly>
                    <img src="img/copy.png" id="copyURLBtn" alt="copy to clipboard icon">
                </div>
                <p class="email-info">Or Send via Email</p>
                <div class="email-container">
                    <form id="emailForm">
                        <div class="filed">
                            <label for="fromEmail">Your email</label>
                            <input type="email" autocomplete="email" required name="from-email" id="fromEmail">
                        </div>

                        <div class="filed">
                            <label for="toEmail">Receiver's email</label>
                            <input type="email" required autocomplete="receiver" name="to-email" id="toEmail">
                        </div>
                        <div class="send-btn-container">
                            <button type="submit">Send</button>
                        </div>
                    </form>
                </div>
            </div>
        </section>
    </div>
    <div class="image-vector"></div>
    <div class="toast">Sample message</div>
    <script src="index.js"></script>
</body>

</html>