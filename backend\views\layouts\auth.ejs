<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/style.css">
    <link rel="stylesheet" href="/css/auth.css">
    <style>
        body {
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .auth-container {
            width: 100%;
            max-width: 450px;
            padding: 20px;
        }
        
        .logo-container {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .logo-container img {
            max-width: 200px;
            height: auto;
        }
        
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .card-header {
            background-color: #fff;
            border-bottom: 1px solid #eee;
            border-top-left-radius: 12px !important;
            border-top-right-radius: 12px !important;
            padding: 1.25rem;
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .alert {
            border-radius: 8px;
            margin-bottom: 1.5rem;
        }
        
        .form-control {
            border-radius: 8px;
            padding: 0.6rem 1rem;
        }
        
        .btn-primary {
            border-radius: 8px;
            padding: 0.6rem 1rem;
            background-color: #4a6cf7;
            border-color: #4a6cf7;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background-color: #3a5cf0;
            border-color: #3a5cf0;
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="logo-container">
            <img src="/img/logo.png" alt="FileForge Logo">
        </div>
        
        <!-- Alert Messages -->
        <% if(typeof errors != 'undefined' && errors.length > 0) { %>
            <% errors.forEach(function(error) { %>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <%= error.msg %>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <% }); %>
        <% } %>
        
        <% if(typeof success_msg != 'undefined' && success_msg != '') { %>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <%= success_msg %>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <% } %>
        
        <% if(typeof error_msg != 'undefined' && error_msg != '') { %>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <%= error_msg %>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <% } %>
        
        <% if(typeof error != 'undefined' && error != '') { %>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <%= error %>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <% } %>
        
        <%- body %>
        
        <div class="text-center mt-4">
            <p class="mb-0">&copy; <%= new Date().getFullYear() %> FileForge. All rights reserved.</p>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Auto dismiss alerts after 5 seconds -->
    <script>
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
</body>
</html> 