<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title || 'FileForge - where files meet seamless sharing' %></title>
    <link rel="shortcut icon" href="/img/favicon.ico" type="image/x-icon">
    <!-- Add Bootstrap CSS before our custom CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Add Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Add our custom CSS after Bootstrap -->
    <link rel="stylesheet" href="/style.css">
    <!-- Add dashboard CSS for dashboard pages -->
    <% if (typeof dashboard !== 'undefined' && dashboard) { %>
    <link rel="stylesheet" href="/dashboard.css">
    <% } %>
    <!-- Add auth CSS for authentication pages -->
    <% if (typeof auth !== 'undefined' && auth) { %>
    <link rel="stylesheet" href="/auth.css">
    <% } %>
    <style>
        /* Fix for collapsed views */
        html, body {
            height: auto !important;
            overflow-y: auto !important;
            min-height: 100vh;
            display: block !important;
        }
        .container {
            padding-top: 20px;
            padding-bottom: 40px;
            min-height: calc(100vh - 120px);
        }
        /* Override any conflicting flex displays */
        .row {
            display: flex !important;
            flex-wrap: wrap !important;
        }
        .card {
            display: block !important;
        }
        /* Ensure alerts are properly displayed */
        .alert {
            display: block;
            width: 100%;
            margin-bottom: 15px;
        }
        
        /* Specific auth page navbar fixes */
        .auth-page .navbar {
            max-height: 60px;
            padding: 0.5rem 1rem !important;
        }
        
        .auth-page .navbar-nav {
            display: flex !important;
            flex-direction: row !important;
        }
        
        .auth-page .nav-item {
            padding: 0 10px;
        }
        
        .auth-page .navbar-brand img {
            height: 25px;
        }
    </style>
</head>
<body class="<%= (typeof auth !== 'undefined' && auth) ? 'auth-page' : '' %> <%= (typeof dashboard !== 'undefined' && dashboard) ? 'dashboard-page' : '' %>">
    <% if (typeof auth !== 'undefined' && auth) { %>
        <%- include('../partials/navbar-auth') %>
    <% } else { %>
        <%- include('../partials/navbar') %>
    <% } %>

    <div class="container mt-4">
        <% if (typeof errors !== 'undefined' || typeof success_msg !== 'undefined' || typeof error_msg !== 'undefined' || typeof info_msg !== 'undefined' || typeof error !== 'undefined') { %>
            <%- include('../partials/messages') %>
        <% } %>
        <%- body %>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto dismiss alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }, 5000);
            });
        });
    </script>
</body>
</html> 