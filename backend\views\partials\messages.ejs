<% if(typeof errors !== 'undefined' && Array.isArray(errors) && errors.length > 0) { %>
    <div class="alert alert-danger alert-dismissible fade show">
        <% errors.forEach(function(error) { %>
            <%= error.msg %><br>
        <% }); %>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<% } %>

<% if(locals.success_msg && typeof success_msg === 'string' && success_msg.trim() !== '') { %>
    <div class="alert alert-success alert-dismissible fade show">
        <%= success_msg %>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<% } %>

<% if(locals.info_msg && typeof info_msg === 'string' && info_msg.trim() !== '') { %>
    <div class="alert alert-info alert-dismissible fade show">
        <%= info_msg %>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<% } %>

<% if(locals.error_msg && typeof error_msg === 'string' && error_msg.trim() !== '') { %>
    <div class="alert alert-danger alert-dismissible fade show">
        <%= error_msg %>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<% } %>

<% if(locals.error && typeof error === 'string' && error.trim() !== '') { %>
    <div class="alert alert-danger alert-dismissible fade show">
        <%= error %>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<% } %> 