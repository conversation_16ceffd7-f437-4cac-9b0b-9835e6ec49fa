<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS and File Upload Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        #fileInput { margin: 10px 0; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>FileForge CORS and File Upload Test</h1>
    <p><strong>Frontend URL:</strong> https://fileforge-indol.vercel.app</p>
    <p><strong>Backend URL:</strong> https://fileforge-backend.vercel.app</p>

    <div class="test-section">
        <h2>1. Deployment Info Test</h2>
        <button onclick="testDeploymentInfo()">Test Deployment Info</button>
        <div id="deploymentResult" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>2. CORS Headers Test</h2>
        <button onclick="testCORS()">Test CORS Headers</button>
        <div id="corsResult" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>3. File Upload Test</h2>
        <input type="file" id="fileInput" accept="*/*">
        <button onclick="testFileUpload()">Test File Upload</button>
        <div id="uploadResult" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>4. Authentication Test</h2>
        <button onclick="testAuth()">Test Authentication</button>
        <div id="authResult" class="test-result"></div>
    </div>

    <script>
        const API_BASE_URL = 'https://fileforge-backend.vercel.app/api';

        function displayResult(elementId, success, message, data = null) {
            const element = document.getElementById(elementId);
            element.className = `test-result ${success ? 'success' : 'error'}`;
            element.innerHTML = `
                <strong>${success ? '✅ SUCCESS' : '❌ ERROR'}:</strong> ${message}
                ${data ? `<pre>${JSON.stringify(data, null, 2)}</pre>` : ''}
            `;
        }

        async function testDeploymentInfo() {
            try {
                console.log('Testing deployment info...');
                const response = await fetch(`${API_BASE_URL}/deployment-info`);
                const data = await response.json();
                
                console.log('Deployment info response:', data);
                
                if (data.corsFixVersion === '3.0') {
                    displayResult('deploymentResult', true, 'Deployment info shows version 3.0 - fixes are deployed!', data);
                } else {
                    displayResult('deploymentResult', false, `Deployment shows version ${data.corsFixVersion} - fixes may not be deployed`, data);
                }
            } catch (error) {
                console.error('Deployment info test failed:', error);
                displayResult('deploymentResult', false, `Failed to get deployment info: ${error.message}`);
            }
        }

        async function testCORS() {
            try {
                console.log('Testing CORS headers...');
                const response = await fetch(`${API_BASE_URL}/test-cors`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ test: 'cors' })
                });

                console.log('CORS response status:', response.status);
                console.log('CORS response headers:', Object.fromEntries([...response.headers]));

                const data = await response.json();
                console.log('CORS response data:', data);

                if (response.ok && data.success) {
                    displayResult('corsResult', true, 'CORS test successful - no CORS errors!', {
                        status: response.status,
                        headers: Object.fromEntries([...response.headers]),
                        data: data
                    });
                } else {
                    displayResult('corsResult', false, 'CORS test failed', data);
                }
            } catch (error) {
                console.error('CORS test failed:', error);
                if (error.message.includes('CORS')) {
                    displayResult('corsResult', false, `CORS Error: ${error.message}`);
                } else {
                    displayResult('corsResult', false, `Network Error: ${error.message}`);
                }
            }
        }

        async function testFileUpload() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];

            if (!file) {
                displayResult('uploadResult', false, 'Please select a file first');
                return;
            }

            try {
                console.log('Testing file upload...');
                console.log('File details:', { name: file.name, size: file.size, type: file.type });

                const formData = new FormData();
                formData.append('myfile', file);

                // Get token from localStorage if available
                const token = localStorage.getItem('token');
                const headers = {};
                if (token && token !== 'undefined' && token !== 'null') {
                    headers['Authorization'] = `Bearer ${token}`;
                    console.log('Using auth token for upload');
                } else {
                    console.log('No auth token available - testing anonymous upload');
                }

                const response = await fetch(`${API_BASE_URL}/files`, {
                    method: 'POST',
                    body: formData,
                    headers,
                    credentials: 'include'
                });

                console.log('Upload response status:', response.status);
                console.log('Upload response headers:', Object.fromEntries([...response.headers]));

                const contentType = response.headers.get('content-type');
                let data;

                if (contentType && contentType.includes('application/json')) {
                    data = await response.json();
                    console.log('Upload response data:', data);
                } else {
                    const textResponse = await response.text();
                    console.log('Upload non-JSON response:', textResponse);
                    throw new Error(`Server returned non-JSON response: ${response.status} ${response.statusText}`);
                }

                if (response.ok && data.success) {
                    displayResult('uploadResult', true, 'File upload successful!', {
                        status: response.status,
                        file: data.file
                    });
                } else {
                    displayResult('uploadResult', false, `Upload failed: ${data.error || 'Unknown error'}`, data);
                }
            } catch (error) {
                console.error('File upload test failed:', error);
                displayResult('uploadResult', false, `Upload Error: ${error.message}`);
            }
        }

        async function testAuth() {
            try {
                console.log('Testing authentication...');
                const response = await fetch(`${API_BASE_URL}/test-auth`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ test: 'auth' })
                });

                const data = await response.json();
                console.log('Auth test response:', data);

                if (response.ok && data.success) {
                    displayResult('authResult', true, 'Authentication endpoint working!', data);
                } else {
                    displayResult('authResult', false, 'Authentication test failed', data);
                }
            } catch (error) {
                console.error('Auth test failed:', error);
                displayResult('authResult', false, `Auth Error: ${error.message}`);
            }
        }

        // Auto-run deployment info test on page load
        window.onload = function() {
            testDeploymentInfo();
        };
    </script>
</body>
</html>
